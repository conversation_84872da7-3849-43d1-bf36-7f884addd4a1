Stack trace:
Frame         Function      Args
0007FFFF89F0  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFF89F0, 0007FFFF78F0) msys-2.0.dll+0x1FE8E
0007FFFF89F0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF8CC8) msys-2.0.dll+0x67F9
0007FFFF89F0  000210046832 (000210286019, 0007FFFF88A8, 0007FFFF89F0, 000000000000) msys-2.0.dll+0x6832
0007FFFF89F0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF89F0  000210068E24 (0007FFFF8A00, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF8CD0  00021006A225 (0007FFFF8A00, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFC45BF0000 ntdll.dll
7FFC45550000 KERNEL32.DLL
7FFC42E50000 KERNELBASE.dll
7FFC45390000 USER32.dll
7FFC425E0000 win32u.dll
000210040000 msys-2.0.dll
7FFC45520000 GDI32.dll
7FFC42310000 gdi32full.dll
7FFC42270000 msvcp_win.dll
7FFC42600000 ucrtbase.dll
7FFC44DB0000 advapi32.dll
7FFC45A20000 msvcrt.dll
7FFC44760000 sechost.dll
7FFC45220000 RPCRT4.dll
7FFC41990000 CRYPTBASE.DLL
7FFC42560000 bcryptPrimitives.dll
7FFC45360000 IMM32.DLL
7FFC45710000 combase.dll
