<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepSeek Chat Pro - محادثة ذكية احترافية</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <style>
        :root {
            --primary-color: #4f46e5;
            --primary-dark: #3730a3;
            --secondary-color: #06b6d4;
            --accent-color: #f59e0b;
            --success-color: #10b981;
            --error-color: #ef4444;
            --warning-color: #f59e0b;
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #f1f5f9;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --border-color: #e2e8f0;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
            --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            --gradient-secondary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        [data-theme="dark"] {
            --bg-primary: #0f172a;
            --bg-secondary: #1e293b;
            --bg-tertiary: #334155;
            --text-primary: #f1f5f9;
            --text-secondary: #94a3b8;
            --border-color: #475569;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', system-ui, -apple-system, sans-serif;
            background: var(--bg-secondary);
            color: var(--text-primary);
            height: 100vh;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .app-container {
            display: flex;
            height: 100vh;
            background: var(--bg-secondary);
        }

        /* Sidebar */
        .sidebar {
            width: 320px;
            background: var(--bg-primary);
            border-left: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            transition: transform 0.3s ease;
            box-shadow: var(--shadow-lg);
        }

        .sidebar.collapsed {
            transform: translateX(100%);
        }

        .sidebar-header {
            padding: 24px;
            border-bottom: 1px solid var(--border-color);
            background: var(--gradient-primary);
            color: white;
        }

        .sidebar-header h2 {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .sidebar-header p {
            opacity: 0.9;
            font-size: 0.875rem;
        }

        .sidebar-actions {
            padding: 16px;
            border-bottom: 1px solid var(--border-color);
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 12px 16px;
            border: none;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            white-space: nowrap;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-secondary {
            background: var(--bg-tertiary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: var(--border-color);
        }

        .btn-icon {
            padding: 12px;
            width: 44px;
            height: 44px;
        }

        .btn-full {
            width: 100%;
        }

        .conversations-list {
            flex: 1;
            overflow-y: auto;
            padding: 16px;
        }

        .conversation-item {
            padding: 16px;
            border-radius: 12px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid transparent;
            position: relative;
        }

        .conversation-item:hover {
            background: var(--bg-tertiary);
            border-color: var(--border-color);
        }

        .conversation-item.active {
            background: var(--primary-color);
            color: white;
        }

        .conversation-title {
            font-weight: 600;
            margin-bottom: 4px;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .conversation-preview {
            font-size: 0.875rem;
            opacity: 0.7;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .conversation-date {
            font-size: 0.75rem;
            opacity: 0.6;
            margin-top: 8px;
        }

        .conversation-actions {
            position: absolute;
            top: 8px;
            left: 8px;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .conversation-item:hover .conversation-actions {
            opacity: 1;
        }

        /* Main Chat Area */
        .main-chat {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: var(--bg-primary);
        }

        .chat-header {
            padding: 20px 24px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: var(--bg-primary);
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .chat-header-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .chat-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .chat-subtitle {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .chat-header-actions {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .status-indicator.connected {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }

        .status-indicator.disconnected {
            background: rgba(239, 68, 68, 0.1);
            color: var(--error-color);
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: currentColor;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Settings Panel */
        .settings-panel {
            background: var(--bg-secondary);
            border-bottom: 1px solid var(--border-color);
            padding: 16px 24px;
            display: none;
        }

        .settings-panel.show {
            display: block;
        }

        .settings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
        }

        .setting-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .setting-label {
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--text-secondary);
        }

        .setting-input {
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--bg-primary);
            color: var(--text-primary);
            font-size: 14px;
        }

        .setting-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        /* Chat Messages */
        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 24px;
            scroll-behavior: smooth;
        }

        .message {
            margin-bottom: 24px;
            display: flex;
            gap: 16px;
            opacity: 0;
            animation: fadeInUp 0.5s ease forwards;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 44px;
            height: 44px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 14px;
            flex-shrink: 0;
        }

        .message.user .message-avatar {
            background: var(--gradient-secondary);
            color: white;
        }

        .message.assistant .message-avatar {
            background: var(--gradient-primary);
            color: white;
        }

        .message-content {
            max-width: 75%;
            padding: 20px;
            border-radius: 20px;
            position: relative;
            word-wrap: break-word;
            line-height: 1.6;
        }

        .message.user .message-content {
            background: var(--gradient-secondary);
            color: white;
            border-bottom-right-radius: 8px;
        }

        .message.assistant .message-content {
            background: var(--bg-secondary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            border-bottom-left-radius: 8px;
        }

        .message-actions {
            position: absolute;
            top: -12px;
            right: 12px;
            display: flex;
            gap: 4px;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .message:hover .message-actions {
            opacity: 1;
        }

        .message-action-btn {
            width: 32px;
            height: 32px;
            border: none;
            border-radius: 8px;
            background: var(--bg-primary);
            color: var(--text-secondary);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            transition: all 0.2s ease;
            box-shadow: var(--shadow-md);
        }

        .message-action-btn:hover {
            background: var(--primary-color);
            color: white;
            transform: scale(1.1);
        }

        /* Code blocks */
        .message-content pre {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 16px;
            margin: 16px 0;
            overflow-x: auto;
            position: relative;
        }

        .message-content code {
            font-family: 'JetBrains Mono', 'Monaco', 'Menlo', monospace;
            font-size: 0.875rem;
        }

        .code-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid var(--border-color);
        }

        .code-language {
            font-size: 0.75rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .copy-code-btn {
            padding: 4px 8px;
            font-size: 0.75rem;
            background: transparent;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .copy-code-btn:hover {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        /* Chat Input */
        .chat-input-container {
            padding: 24px;
            background: var(--bg-primary);
            border-top: 1px solid var(--border-color);
        }

        .chat-input-wrapper {
            position: relative;
            background: var(--bg-secondary);
            border: 2px solid var(--border-color);
            border-radius: 16px;
            transition: all 0.3s ease;
        }

        .chat-input-wrapper:focus-within {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(79, 70, 229, 0.1);
        }

        .chat-input {
            width: 100%;
            min-height: 56px;
            max-height: 200px;
            padding: 16px 60px 16px 20px;
            border: none;
            background: transparent;
            color: var(--text-primary);
            font-size: 16px;
            font-family: inherit;
            resize: none;
            outline: none;
        }

        .chat-input::placeholder {
            color: var(--text-secondary);
        }

        .input-actions {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            gap: 8px;
        }

        .send-btn {
            width: 40px;
            height: 40px;
            background: var(--gradient-primary);
            border: none;
            border-radius: 10px;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .send-btn:hover:not(:disabled) {
            transform: scale(1.1);
            box-shadow: var(--shadow-md);
        }

        .send-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* Loading Animation */
        .typing-indicator {
            display: none;
            padding: 20px;
            text-align: center;
            color: var(--text-secondary);
        }

        .typing-indicator.show {
            display: block;
        }

        .typing-dots {
            display: inline-flex;
            gap: 4px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            background: var(--primary-color);
            border-radius: 50%;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typing {
            0%, 80%, 100% {
                transform: scale(0);
                opacity: 0.5;
            }
            40% {
                transform: scale(1);
                opacity: 1;
            }
        }

        /* Toast Notifications */
        .toast-container {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .toast {
            padding: 16px 24px;
            border-radius: 12px;
            color: white;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 12px;
            box-shadow: var(--shadow-lg);
            transform: translateY(-100px);
            opacity: 0;
            animation: slideDown 0.3s ease forwards;
        }

        .toast.success {
            background: var(--success-color);
        }

        .toast.error {
            background: var(--error-color);
        }

        .toast.info {
            background: var(--primary-color);
        }

        @keyframes slideDown {
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .sidebar {
                position: absolute;
                top: 0;
                right: 0;
                height: 100%;
                z-index: 100;
                width: 100%;
                max-width: 350px;
            }

            .chat-header {
                padding: 16px;
            }

            .chat-messages {
                padding: 16px;
            }

            .message-content {
                max-width: 85%;
            }

            .chat-input-container {
                padding: 16px;
            }

            .settings-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Scrollbar Styling */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-secondary);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--text-secondary);
        }

        /* Custom animations */
        .slide-in-right {
            animation: slideInRight 0.3s ease forwards;
        }

        .slide-out-right {
            animation: slideOutRight 0.3s ease forwards;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
            }
            to {
                transform: translateX(0);
            }
        }

        @keyframes slideOutRight {
            from {
                transform: translateX(0);
            }
            to {
                transform: translateX(100%);
            }
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-secondary);
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-state h3 {
            font-size: 1.5rem;
            margin-bottom: 12px;
            color: var(--text-primary);
        }

        .empty-state p {
            font-size: 1rem;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Sidebar -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-robot"></i> DeepSeek Pro</h2>
                <p>محادثة ذكية متقدمة</p>
            </div>

            <div class="sidebar-actions">
                <button class="btn btn-primary btn-full" id="newChatBtn">
                    <i class="fas fa-plus"></i>
                    محادثة جديدة
                </button>
                <div style="margin-top: 12px;">
                    <input type="text" class="setting-input" id="searchInput" placeholder="بحث في المحادثات..." style="font-size: 14px;">
                </div>
            </div>

            <div class="conversations-list" id="conversationsList">
                <div class="empty-state">
                    <i class="fas fa-comments"></i>
                    <h3>لا توجد محادثات</h3>
                    <p>ابدأ محادثة جديدة للبدء</p>
                </div>
            </div>
        </div>

        <!-- Main Chat Area -->
        <div class="main-chat">
            <div class="chat-header">
                <div class="chat-header-left">
                    <button class="btn btn-icon btn-secondary" id="toggleSidebar">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div>
                        <div class="chat-title" id="chatTitle">محادثة جديدة</div>
                        <div class="chat-subtitle">DeepSeek R1 Model</div>
                    </div>
                </div>
                <div class="chat-header-actions">
                    <div class="status-indicator disconnected" id="connectionStatus">
                        <div class="status-dot"></div>
                        <span>غير متصل</span>
                    </div>
                    <button class="btn btn-icon btn-secondary" id="toggleSettings">
                        <i class="fas fa-cog"></i>
                    </button>
                    <button class="btn btn-icon btn-secondary" id="toggleTheme">
                        <i class="fas fa-moon"></i>
                    </button>
                </div>
            </div>

            <!-- Settings Panel -->
            <div class="settings-panel" id="settingsPanel">
                <div class="settings-grid">
                    <div class="setting-group">
                        <label class="setting-label">OpenRouter API Key</label>
                        <input type="password" class="setting-input" id="apiKeyInput" placeholder="sk-or-v1-...">
                    </div>
                    <div class="setting-group">
                        <label class="setting-label">درجة الحرارة (Temperature)</label>
                        <input type="range" class="setting-input" id="temperatureSlider" min="0" max="2" step="0.1" value="0.7">
                        <span id="temperatureValue">0.7</span>
                    </div>
                    <div class="setting-group">
                        <label class="setting-label">الحد الأقصى للرموز</label>
                        <input type="number" class="setting-input" id="maxTokensInput" value="2000" min="100" max="4000">
                    </div>
                    <div class="setting-group">
                        <label class="setting-label">النموذج</label>
                        <select class="setting-input" id="modelSelect">
                            <option value="deepseek/deepseek-r1-0528:free">DeepSeek R1 (Free)</option>
                            <option value="deepseek/deepseek-r1">DeepSeek R1 (Paid)</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="chat-messages" id="chatMessages">
                <div class="message assistant">
                    <div class="message-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="message-content">
                        <div class="message-actions">
                            <button class="message-action-btn" title="نسخ">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                        مرحباً! أنا DeepSeek Pro، مساعدك الذكي المتقدم. يمكنني مساعدتك في البرمجة، الكتابة، حل المشاكل، والمزيد. كيف يمكنني مساعدتك اليوم؟
                    </div>
                </div>
            </div>

            <div class="typing-indicator" id="typingIndicator">
                <div style="display: flex; align-items: center; gap: 12px; justify-content: center;">
                    <div class="typing-dots">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                    <span>DeepSeek يفكر...</span>
                </div>
            </div>

            <div class="chat-input-container">
                <div class="chat-input-wrapper">
                    <textarea class="chat-input" id="messageInput" placeholder="اكتب رسالتك هنا... (Enter للإرسال، Shift+Enter لسطر جديد)" rows="1"></textarea>
                    <div class="input-actions">
                        <button class="send-btn" id="sendBtn">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div class="toast-container" id="toastContainer"></div>

    <script>
        class DeepSeekChatPro {
            constructor() {
                this.currentConversationId = null;
                this.conversations = this.loadConversations();
                this.settings = this.loadSettings();
                this.isDarkMode = localStorage.getItem('darkMode') === 'true';
                
                this.initializeElements();
                this.bindEvents();
                this.initializeUI();
                this.loadCurrentConversation();
            }

            initializeElements() {
                // Sidebar elements
                this.sidebar = document.getElementById('sidebar');
                this.toggleSidebarBtn = document.getElementById('toggleSidebar');
                this.newChatBtn = document.getElementById('newChatBtn');
                this.conversationsList = document.getElementById('conversationsList');
                this.searchInput = document.getElementById('searchInput');

                // Header elements
                this.chatTitle = document.getElementById('chatTitle');
                this.connectionStatus = document.getElementById('connectionStatus');
                this.toggleSettingsBtn = document.getElementById('toggleSettings');
                this.toggleThemeBtn = document.getElementById('toggleTheme');

                // Settings elements
                this.settingsPanel = document.getElementById('settingsPanel');
                this.apiKeyInput = document.getElementById('apiKeyInput');
                this.temperatureSlider = document.getElementById('temperatureSlider');
                this.temperatureValue = document.getElementById('temperatureValue');
                this.maxTokensInput = document.getElementById('maxTokensInput');
                this.modelSelect = document.getElementById('modelSelect');

                // Chat elements
                this.chatMessages = document.getElementById('chatMessages');
                this.messageInput = document.getElementById('messageInput');
                this.sendBtn = document.getElementById('sendBtn');
                this.typingIndicator = document.getElementById('typingIndicator');
                this.toastContainer = document.getElementById('toastContainer');
            }

            bindEvents() {
                // Sidebar events
                this.toggleSidebarBtn.addEventListener('click', () => this.toggleSidebar());
                this.newChatBtn.addEventListener('click', () => this.createNewConversation());
                this.searchInput.addEventListener('input', (e) => this.handleSearch(e));

                // Header events
                this.toggleSettingsBtn.addEventListener('click', () => this.toggleSettings());
                this.toggleThemeBtn.addEventListener('click', () => this.toggleTheme());

                // Settings events
                this.apiKeyInput.addEventListener('input', (e) => this.handleApiKeyChange(e));
                this.temperatureSlider.addEventListener('input', (e) => this.handleTemperatureChange(e));
                this.maxTokensInput.addEventListener('input', (e) => this.handleMaxTokensChange(e));
                this.modelSelect.addEventListener('change', (e) => this.handleModelChange(e));

                // Chat events
                this.messageInput.addEventListener('keydown', (e) => this.handleKeyPress(e));
                this.messageInput.addEventListener('input', (e) => this.autoResize(e.target));
                this.sendBtn.addEventListener('click', () => this.sendMessage());

                // Global events
                document.addEventListener('click', (e) => this.handleGlobalClick(e));
            }

            initializeUI() {
                // Apply theme
                if (this.isDarkMode) {
                    document.documentElement.setAttribute('data-theme', 'dark');
                    this.toggleThemeBtn.innerHTML = '<i class="fas fa-sun"></i>';
                }

                // Load settings
                this.apiKeyInput.value = this.settings.apiKey || '';
                this.temperatureSlider.value = this.settings.temperature || 0.7;
                this.temperatureValue.textContent = this.settings.temperature || 0.7;
                this.maxTokensInput.value = this.settings.maxTokens || 2000;
                this.modelSelect.value = this.settings.model || 'deepseek/deepseek-r1-0528:free';

                // Update connection status
                this.updateConnectionStatus();

                // Render conversations
                this.renderConversations();
            }

            // Settings Management
            loadSettings() {
                const savedSettings = localStorage.getItem('deepseek_pro_settings');
                return savedSettings ? JSON.parse(savedSettings) : {
                    apiKey: '',
                    temperature: 0.7,
                    maxTokens: 2000,
                    model: 'deepseek/deepseek-r1-0528:free'
                };
            }

            saveSettings() {
                localStorage.setItem('deepseek_pro_settings', JSON.stringify(this.settings));
                this.updateConnectionStatus();
            }

            handleApiKeyChange(e) {
                this.settings.apiKey = e.target.value.trim();
                this.saveSettings();
            }

            handleTemperatureChange(e) {
                this.settings.temperature = parseFloat(e.target.value);
                this.temperatureValue.textContent = this.settings.temperature;
                this.saveSettings();
            }

            handleMaxTokensChange(e) {
                this.settings.maxTokens = parseInt(e.target.value);
                this.saveSettings();
            }

            handleModelChange(e) {
                this.settings.model = e.target.value;
                this.saveSettings();
            }

            updateConnectionStatus() {
                const isConnected = this.settings.apiKey && this.settings.apiKey.length > 0;
                this.connectionStatus.className = `status-indicator ${isConnected ? 'connected' : 'disconnected'}`;
                this.connectionStatus.innerHTML = `
                    <div class="status-dot"></div>
                    <span>${isConnected ? 'متصل' : 'غير متصل'}</span>
                `;
            }

            // Conversation Management
            loadConversations() {
                const saved = localStorage.getItem('deepseek_pro_conversations');
                return saved ? JSON.parse(saved) : [];
            }

            saveConversations() {
                localStorage.setItem('deepseek_pro_conversations', JSON.stringify(this.conversations));
            }

            createNewConversation() {
                const conversationId = 'conv_' + Date.now();
                const newConversation = {
                    id: conversationId,
                    title: 'محادثة جديدة',
                    messages: [],
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                };

                this.conversations.unshift(newConversation);
                this.saveConversations();
                this.loadConversation(conversationId);
                this.renderConversations();
                this.showToast('تم إنشاء محادثة جديدة', 'success');
            }

            loadConversation(conversationId) {
                this.currentConversationId = conversationId;
                const conversation = this.conversations.find(c => c.id === conversationId);
                
                if (conversation) {
                    this.chatTitle.textContent = conversation.title;
                    this.renderMessages(conversation.messages);
                    localStorage.setItem('current_conversation_id', conversationId);
                }
            }

            loadCurrentConversation() {
                const savedConversationId = localStorage.getItem('current_conversation_id');
                if (savedConversationId && this.conversations.find(c => c.id === savedConversationId)) {
                    this.loadConversation(savedConversationId);
                } else if (this.conversations.length === 0) {
                    this.createNewConversation();
                } else {
                    this.loadConversation(this.conversations[0].id);
                }
            }

            updateConversationTitle(conversationId, title) {
                const conversation = this.conversations.find(c => c.id === conversationId);
                if (conversation) {
                    conversation.title = title;
                    conversation.updatedAt = new Date().toISOString();
                    this.saveConversations();
                    this.renderConversations();
                    if (conversationId === this.currentConversationId) {
                        this.chatTitle.textContent = title;
                    }
                }
            }

            deleteConversation(conversationId) {
                if (confirm('هل أنت متأكد من حذف هذه المحادثة؟')) {
                    this.conversations = this.conversations.filter(c => c.id !== conversationId);
                    this.saveConversations();
                    
                    if (conversationId === this.currentConversationId) {
                        if (this.conversations.length > 0) {
                            this.loadConversation(this.conversations[0].id);
                        } else {
                            this.createNewConversation();
                        }
                    }
                    
                    this.renderConversations();
                    this.showToast('تم حذف المحادثة', 'success');
                }
            }

            renderConversations(filteredConversations = null) {
                const conversationsToRender = filteredConversations || this.conversations;
                
                if (conversationsToRender.length === 0) {
                    const emptyMessage = filteredConversations ? 'لا توجد نتائج للبحث' : 'لا توجد محادثات';
                    this.conversationsList.innerHTML = `
                        <div class="empty-state">
                            <i class="fas fa-${filteredConversations ? 'search' : 'comments'}"></i>
                            <h3>${emptyMessage}</h3>
                            <p>${filteredConversations ? 'جرب كلمات بحث أخرى' : 'ابدأ محادثة جديدة للبدء'}</p>
                        </div>
                    `;
                    return;
                }

                this.conversationsList.innerHTML = conversationsToRender.map(conv => {
                    const preview = conv.messages.length > 0 
                        ? conv.messages[conv.messages.length - 1].content.substring(0, 100) + '...'
                        : 'محادثة فارغة';
                    
                    const date = new Date(conv.updatedAt).toLocaleDateString('ar-SA', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric'
                    });

                    return `
                        <div class="conversation-item ${conv.id === this.currentConversationId ? 'active' : ''}" 
                             onclick="chatApp.loadConversation('${conv.id}')">
                            <div class="conversation-actions">
                                <button class="btn btn-icon" style="width: 24px; height: 24px; font-size: 12px;" 
                                        onclick="event.stopPropagation(); chatApp.deleteConversation('${conv.id}')" 
                                        title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                            <div class="conversation-title">${conv.title}</div>
                            <div class="conversation-preview">${preview}</div>
                            <div class="conversation-date">${date}</div>
                        </div>
                    `;
                }).join('');
            }

            // Message Management
            addMessage(role, content) {
                const conversation = this.conversations.find(c => c.id === this.currentConversationId);
                if (!conversation) return;

                const message = {
                    id: 'msg_' + Date.now(),
                    role,
                    content,
                    timestamp: new Date().toISOString()
                };

                conversation.messages.push(message);
                conversation.updatedAt = new Date().toISOString();

                // Auto-generate title from first user message
                if (role === 'user' && conversation.messages.filter(m => m.role === 'user').length === 1) {
                    const title = content.length > 30 ? content.substring(0, 30) + '...' : content;
                    this.updateConversationTitle(this.currentConversationId, title);
                }

                this.saveConversations();
                this.renderMessage(message);
                this.scrollToBottom();
            }

            renderMessages(messages) {
                // Keep the welcome message, clear others
                const welcomeMessage = this.chatMessages.querySelector('.message.assistant');
                this.chatMessages.innerHTML = '';
                if (messages.length === 0 && welcomeMessage) {
                    this.chatMessages.appendChild(welcomeMessage);
                }

                messages.forEach(message => this.renderMessage(message));
            }

            renderMessage(message) {
                const messageEl = document.createElement('div');
                messageEl.className = `message ${message.role}`;
                
                const avatar = message.role === 'user' 
                    ? '<i class="fas fa-user"></i>' 
                    : '<i class="fas fa-robot"></i>';

                const processedContent = this.processMessageContent(message.content);

                messageEl.innerHTML = `
                    <div class="message-avatar">${avatar}</div>
                    <div class="message-content">
                        <div class="message-actions">
                            <button class="message-action-btn" onclick="chatApp.copyText('${message.id}')" title="نسخ">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                        ${processedContent}
                    </div>
                `;

                this.chatMessages.appendChild(messageEl);
                
                // Add copy buttons to code blocks
                this.addCodeCopyButtons(messageEl);
            }

            processMessageContent(content) {
                // Process code blocks
                content = content.replace(/```(\w*)\n([\s\S]*?)```/g, (match, lang, code) => {
                    return `
                        <div class="code-header">
                            <span class="code-language">${lang || 'code'}</span>
                            <button class="copy-code-btn" onclick="chatApp.copyCode(this)">
                                <i class="fas fa-copy"></i> نسخ
                            </button>
                        </div>
                        <pre><code class="language-${lang}">${this.escapeHtml(code.trim())}</code></pre>
                    `;
                });

                // Process inline code
                content = content.replace(/`([^`]+)`/g, '<code>$1</code>');

                // Process line breaks
                content = content.replace(/\n/g, '<br>');

                return content;
            }

            addCodeCopyButtons(messageEl) {
                const codeBlocks = messageEl.querySelectorAll('pre code');
                codeBlocks.forEach(block => {
                    if (typeof Prism !== 'undefined') {
                        Prism.highlightElement(block);
                    }
                });
            }

            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }

            // Chat Functionality
            async sendMessage() {
                const message = this.messageInput.value.trim();
                if (!message) return;

                if (!this.settings.apiKey) {
                    this.showToast('يرجى إدخال API Key في الإعدادات', 'error');
                    this.toggleSettings();
                    return;
                }

                // Add user message
                this.addMessage('user', message);
                this.messageInput.value = '';
                this.autoResize(this.messageInput);
                this.showTyping(true);
                this.sendBtn.disabled = true;

                try {
                    const response = await this.callAPI(message);
                    this.addMessage('assistant', response);
                } catch (error) {
                    this.showToast('حدث خطأ: ' + error.message, 'error');
                    console.error('API Error:', error);
                } finally {
                    this.showTyping(false);
                    this.sendBtn.disabled = false;
                    this.messageInput.focus();
                }
            }

            async callAPI(message) {
                const conversation = this.conversations.find(c => c.id === this.currentConversationId);
                const messages = conversation ? conversation.messages.map(m => ({
                    role: m.role,
                    content: m.content
                })) : [];

                const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
                    method: "POST",
                    headers: {
                        "Authorization": `Bearer ${this.settings.apiKey}`,
                        "HTTP-Referer": window.location.origin,
                        "X-Title": "DeepSeek Chat Pro",
                        "Content-Type": "application/json"
                    },
                    body: JSON.stringify({
                        model: this.settings.model,
                        messages: messages,
                        temperature: this.settings.temperature,
                        max_tokens: this.settings.maxTokens
                    })
                });

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(errorData.error?.message || `HTTP ${response.status}`);
                }

                const data = await response.json();
                return data.choices[0]?.message?.content || 'لا توجد إجابة';
            }

            // UI Helpers
            toggleSidebar() {
                this.sidebar.classList.toggle('collapsed');
            }

            toggleSettings() {
                this.settingsPanel.classList.toggle('show');
            }

            toggleTheme() {
                this.isDarkMode = !this.isDarkMode;
                localStorage.setItem('darkMode', this.isDarkMode);
                
                if (this.isDarkMode) {
                    document.documentElement.setAttribute('data-theme', 'dark');
                    this.toggleThemeBtn.innerHTML = '<i class="fas fa-sun"></i>';
                } else {
                    document.documentElement.removeAttribute('data-theme');
                    this.toggleThemeBtn.innerHTML = '<i class="fas fa-moon"></i>';
                }
            }

            showTyping(show) {
                this.typingIndicator.classList.toggle('show', show);
                if (show) this.scrollToBottom();
            }

            scrollToBottom() {
                setTimeout(() => {
                    this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
                }, 100);
            }

            autoResize(textarea) {
                textarea.style.height = 'auto';
                textarea.style.height = Math.min(textarea.scrollHeight, 200) + 'px';
            }

            handleKeyPress(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendMessage();
                }
            }

            handleGlobalClick(e) {
                // Close sidebar on mobile when clicking outside
                if (window.innerWidth <= 768 && 
                    !this.sidebar.contains(e.target) && 
                    !this.toggleSidebarBtn.contains(e.target) &&
                    !this.sidebar.classList.contains('collapsed')) {
                    this.toggleSidebar();
                }
            }

            // Utility Functions
            copyText(messageId) {
                const conversation = this.conversations.find(c => c.id === this.currentConversationId);
                const message = conversation?.messages.find(m => m.id === messageId);
                
                if (message) {
                    navigator.clipboard.writeText(message.content).then(() => {
                        this.showToast('تم نسخ النص', 'success');
                    });
                }
            }

            copyCode(button) {
                const codeBlock = button.closest('.message-content').querySelector('pre code');
                if (codeBlock) {
                    navigator.clipboard.writeText(codeBlock.textContent).then(() => {
                        button.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
                        setTimeout(() => {
                            button.innerHTML = '<i class="fas fa-copy"></i> نسخ';
                        }, 2000);
                    });
                }
            }

            handleSearch(e) {
                const searchTerm = e.target.value.toLowerCase().trim();
                
                if (!searchTerm) {
                    this.renderConversations();
                    return;
                }

                const filteredConversations = this.conversations.filter(conv => {
                    const titleMatch = conv.title.toLowerCase().includes(searchTerm);
                    const contentMatch = conv.messages.some(msg => 
                        msg.content.toLowerCase().includes(searchTerm)
                    );
                    return titleMatch || contentMatch;
                });

                this.renderConversations(filteredConversations);
            }

            showToast(message, type = 'info') {
                const toast = document.createElement('div');
                toast.className = `toast ${type}`;
                
                const icon = type === 'success' ? 'check' : type === 'error' ? 'exclamation-triangle' : 'info-circle';
                toast.innerHTML = `
                    <i class="fas fa-${icon}"></i>
                    <span>${message}</span>
                `;

                this.toastContainer.appendChild(toast);

                setTimeout(() => {
                    toast.remove();
                }, 4000);
            }
        }

        // Initialize the application
        let chatApp;
        document.addEventListener('DOMContentLoaded', () => {
            chatApp = new DeepSeekChatPro();
        });
    </script>
</body>
</html>
