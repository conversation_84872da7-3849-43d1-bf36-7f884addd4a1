
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepSeek Chat - محادثة ذكية</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .chat-container {
            width: 90%;
            max-width: 800px;
            height: 90vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .chat-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }

        .chat-header h1 {
            font-size: 1.8rem;
            margin-bottom: 5px;
        }

        .chat-header p {
            opacity: 0.9;
            font-size: 0.9rem;
        }

        .api-config {
            background: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
        }

        .api-input-group {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .api-input {
            flex: 1;
            padding: 10px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .api-input:focus {
            outline: none;
            border-color: #4facfe;
        }

        .api-status {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #dc3545;
            transition: background-color 0.3s;
        }

        .api-status.connected {
            background: #28a745;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            font-size: 14px;
        }

        .message.user .message-avatar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .message.assistant .message-avatar {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .message-content {
            max-width: 70%;
            padding: 15px;
            border-radius: 18px;
            word-wrap: break-word;
            line-height: 1.5;
        }

        .message.user .message-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-bottom-right-radius: 4px;
        }

        .message.assistant .message-content {
            background: white;
            color: #333;
            border: 1px solid #e9ecef;
            border-bottom-left-radius: 4px;
        }

        .chat-input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
        }

        .chat-input-group {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }

        .chat-input {
            flex: 1;
            min-height: 50px;
            max-height: 120px;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 16px;
            font-family: inherit;
            resize: none;
            transition: border-color 0.3s;
        }

        .chat-input:focus {
            outline: none;
            border-color: #4facfe;
        }

        .send-button {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border: none;
            border-radius: 50%;
            color: white;
            cursor: pointer;
            transition: transform 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .send-button:hover {
            transform: scale(1.1);
        }

        .send-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #6c757d;
        }

        .loading.show {
            display: block;
        }

        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4facfe;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            border: 1px solid #f5c6cb;
        }

        @media (max-width: 768px) {
            .chat-container {
                width: 95%;
                height: 95vh;
                border-radius: 15px;
            }
            
            .chat-header h1 {
                font-size: 1.4rem;
            }
            
            .message-content {
                max-width: 85%;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <h1>DeepSeek Chat</h1>
            <p>محادثة ذكية مدعومة بالذكاء الاصطناعي</p>
        </div>

        <div class="api-config">
            <div class="api-input-group">
                <input type="password" class="api-input" id="apiKey" placeholder="أدخل OpenRouter API Key">
                <div class="api-status" id="apiStatus"></div>
            </div>
        </div>

        <div class="chat-messages" id="chatMessages">
            <div class="message assistant">
                <div class="message-avatar">AI</div>
                <div class="message-content">
                    مرحباً! أنا مساعد ذكي مدعوم بنموذج DeepSeek R1. كيف يمكنني مساعدتك اليوم؟
                </div>
            </div>
        </div>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <span>جاري التفكير...</span>
        </div>

        <div class="chat-input-container">
            <div class="chat-input-group">
                <textarea class="chat-input" id="messageInput" placeholder="اكتب رسالتك هنا..." rows="1"></textarea>
                <button class="send-button" id="sendButton">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="22" y1="2" x2="11" y2="13"></line>
                        <polygon points="22,2 15,22 11,13 2,9"></polygon>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <script>
        class ChatApp {
            constructor() {
                this.apiKey = '';
                this.isConnected = false;
                this.initializeElements();
                this.bindEvents();
                this.loadApiKey();
            }

            initializeElements() {
                this.apiKeyInput = document.getElementById('apiKey');
                this.apiStatus = document.getElementById('apiStatus');
                this.chatMessages = document.getElementById('chatMessages');
                this.messageInput = document.getElementById('messageInput');
                this.sendButton = document.getElementById('sendButton');
                this.loading = document.getElementById('loading');
            }

            bindEvents() {
                this.apiKeyInput.addEventListener('input', (e) => this.handleApiKeyInput(e));
                this.messageInput.addEventListener('keypress', (e) => this.handleKeyPress(e));
                this.messageInput.addEventListener('input', (e) => this.autoResize(e.target));
                this.sendButton.addEventListener('click', () => this.sendMessage());
            }

            loadApiKey() {
                const savedKey = localStorage.getItem('deepseek_api_key');
                if (savedKey) {
                    this.apiKeyInput.value = savedKey;
                    this.handleApiKeyInput({ target: { value: savedKey } });
                }
            }

            handleApiKeyInput(e) {
                this.apiKey = e.target.value.trim();
                this.isConnected = this.apiKey.length > 0;
                
                if (this.isConnected) {
                    this.apiStatus.classList.add('connected');
                    localStorage.setItem('deepseek_api_key', this.apiKey);
                } else {
                    this.apiStatus.classList.remove('connected');
                    localStorage.removeItem('deepseek_api_key');
                }
            }

            handleKeyPress(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendMessage();
                }
            }

            autoResize(textarea) {
                textarea.style.height = 'auto';
                textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
            }

            async sendMessage() {
                const message = this.messageInput.value.trim();
                
                if (!message) return;
                if (!this.isConnected) {
                    this.showError('يرجى إدخال API Key صحيح أولاً');
                    return;
                }

                this.addMessage('user', message);
                this.messageInput.value = '';
                this.messageInput.style.height = 'auto';
                this.showLoading(true);
                this.sendButton.disabled = true;

                try {
                    const response = await this.callDeepSeekAPI(message);
                    this.addMessage('assistant', response);
                } catch (error) {
                    this.showError('حدث خطأ: ' + error.message);
                } finally {
                    this.showLoading(false);
                    this.sendButton.disabled = false;
                    this.messageInput.focus();
                }
            }

            async callDeepSeekAPI(message) {
                const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
                    method: "POST",
                    headers: {
                        "Authorization": `Bearer ${this.apiKey}`,
                        "HTTP-Referer": window.location.origin,
                        "X-Title": "DeepSeek Chat App",
                        "Content-Type": "application/json"
                    },
                    body: JSON.stringify({
                        "model": "deepseek/deepseek-r1-0528:free",
                        "messages": [
                            {
                                "role": "user",
                                "content": message
                            }
                        ],
                        "temperature": 0.7,
                        "max_tokens": 2000
                    })
                });

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(errorData.error?.message || `HTTP ${response.status}`);
                }

                const data = await response.json();
                return data.choices[0]?.message?.content || 'لا توجد إجابة';
            }

            addMessage(role, content) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${role}`;
                
                const avatar = document.createElement('div');
                avatar.className = 'message-avatar';
                avatar.textContent = role === 'user' ? 'أنت' : 'AI';
                
                const messageContent = document.createElement('div');
                messageContent.className = 'message-content';
                messageContent.textContent = content;
                
                messageDiv.appendChild(avatar);
                messageDiv.appendChild(messageContent);
                
                this.chatMessages.appendChild(messageDiv);
                this.scrollToBottom();
            }

            scrollToBottom() {
                this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
            }

            showLoading(show) {
                this.loading.classList.toggle('show', show);
                if (show) {
                    this.scrollToBottom();
                }
            }

            showError(message) {
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error-message';
                errorDiv.textContent = message;
                
                this.chatMessages.appendChild(errorDiv);
                this.scrollToBottom();
                
                setTimeout(() => {
                    errorDiv.remove();
                }, 5000);
            }
        }

        document.addEventListener('DOMContentLoaded', () => {
            new ChatApp();
        });
    </script>
</body>
</html>
